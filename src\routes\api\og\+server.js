import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';
import { error } from '@sveltejs/kit';

const CACHE_CONTROL = 'public, max-age=604800, stale-while-revalidate=86400';
// IMPORTANT: For production, it's highly recommended to download this font
// and serve it from your own static assets or a reliable CDN you control.
const FONT_URL = 'http://pixeldrain.com/api/file/52yBhNXR';
const FONT_NAME = 'Gilroy-SemiBold'; // Should match the font's actual name

function generateCacheKey(params) {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  return Buffer.from(sortedParams).toString('base64').replace(/[/+=]/g, '');
}

export const GET = async ({ url, setHeaders }) => {
  try {
    // --- Font Loading ---
    // This is fetched for every request. Consider caching this data if possible.
    const fontFileResponse = await fetch(FONT_URL);
    if (!fontFileResponse.ok) {
      console.error('Failed to load font:', fontFileResponse.status, await fontFileResponse.text());
      throw error(500, 'Critical error: Font asset could not be loaded.');
    }
    const fontData = await fontFileResponse.arrayBuffer();

    const satoriOptions = {
      width: 1200,
      height: 630,
      fonts: [
        {
          name: FONT_NAME,
          data: fontData,
          weight: 600, // Corresponds to SemiBold
          style: 'normal',
        },
        {
          name: FONT_NAME, // Satori needs explicit font weights
          data: fontData, // Using the same font file; Satori will use this for 'font-bold' or weight: 700
          weight: 700, // Corresponds to Bold
          style: 'normal',
        },
      ],
      // debug: true, // Uncomment for Satori debug output
    };

    // --- Parameter & Template Selection ---
    const requestType = url.searchParams.get('type') || 'home';
    let params;
    let satoriNode;

    if (requestType === 'home' || requestType === 'static_variant') {
      // --- Static Variant ---
      // For the main page, highlighting site features to encourage visits.
      params = {
        title: 'anithing.moe',
        subtitle: 'Your Ultimate Gateway to Japanese Media',
        description: 'Search, track, and manage anime, manga, VNs, and LNs across all platforms. Discover what to watch, read, or play next!',
        type: 'home_page_showcase'
      };

      /*
      NOTE on CSS Compatibility:
      - `filter: blur()` used for blobs in the original example is NOT supported by Satori.
        The "blobs" will render as solid circles/ovals.
      - `backdrop-blur` for badges is also NOT supported.
      - Tailwind CSS classes are generally well-supported by `satori-html`, especially for layout and text.
      - Gradients (`bg-gradient-to-r`) are supported by Satori.
      */
      satoriNode = html`
        <div tw="flex flex-col w-full h-full relative overflow-hidden bg-gray-900 text-white" style="font-family: '${FONT_NAME}';">
          <!-- Background Gradient -->
          <div tw="absolute inset-0 bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"></div>
          
          <!-- Decorative Blobs (will be solid, not blurred) -->
          <div tw="absolute top-[-10%] left-[-10%] w-[600px] h-[600px] bg-purple-600 rounded-full opacity-30"></div>
          <div tw="absolute bottom-[-10%] right-[-10%] w-[500px] h-[500px] bg-sky-500 rounded-full opacity-30"></div>
          <div tw="absolute top-[20%] right-[5%] w-[300px] h-[300px] bg-pink-500 rounded-full opacity-20"></div>

          <!-- Content Area -->
          <div tw="flex flex-col w-full h-full px-16 py-12 justify-center items-center relative z-10">
            <div tw="flex flex-col items-center mb-6">
              <h1 tw="text-white text-7xl font-bold mb-3 text-center" style="font-weight: 700;">
                ${params.title}
              </h1>
              <div tw="w-32 h-1.5 bg-gradient-to-r from-purple-500 via-sky-400 to-pink-500 rounded-full"></div>
            </div>

            <h2 tw="text-sky-300 text-4xl font-semibold mb-5 text-center max-w-3xl" style="font-weight: 600;">
              ${params.subtitle}
            </h2>

            <p tw="text-slate-300 text-2xl text-center max-w-4xl leading-relaxed mb-8">
              ${params.description}
            </p>

            <div tw="flex flex-wrap justify-center gap-3 mb-8">
              <div tw="flex items-center px-5 py-2.5 bg-purple-700/40 rounded-full border border-purple-500/50">
                <span tw="text-purple-300 text-lg font-medium">🔍 Unified Search</span>
              </div>
              <div tw="flex items-center px-5 py-2.5 bg-sky-700/40 rounded-full border border-sky-500/50">
                <span tw="text-sky-300 text-lg font-medium">📚 Centralized Lists</span>
              </div>
              <div tw="flex items-center px-5 py-2.5 bg-pink-700/40 rounded-full border border-pink-500/50">
                <span tw="text-pink-300 text-lg font-medium">👀 Track Friends</span>
              </div>
            </div>
            
            <div tw="flex items-center mt-4 px-8 py-3 bg-green-600/50 rounded-full border border-green-400/40 shadow-lg">
              <span tw="text-green-200 text-xl font-semibold">✨ Join the Community & Explore! ✨</span>
            </div>

            <div tw="absolute bottom-6 left-1/2" style="transform: translateX(-50%);">
              <div tw="text-slate-400 text-base">All your media, aniwhere.</div>
            </div>
          </div>
        </div>
      `;
    } else {
      // --- Dynamic Variant ---
      // For individual content pages (e.g., anime, manga details).
      const dynamicTitle = url.searchParams.get('title') || 'anithing.moe';
      const dynamicSubtitle = url.searchParams.get('subtitle'); // Optional
      const dynamicDescription = url.searchParams.get('description'); // Optional
      const coverImageUrl = url.searchParams.get('image'); // Optional image URL for background

      params = {
        title: dynamicTitle.substring(0, 80), // Max length for title
        subtitle: dynamicSubtitle ? dynamicSubtitle.substring(0, 120) : null,
        description: dynamicDescription ? dynamicDescription.substring(0, 200) : null,
        image: coverImageUrl, // Store validated/processed URL if needed
        type: requestType.substring(0, 30) // Sanitize type
      };

      satoriNode = html`
        <div tw="flex w-full h-full relative overflow-hidden bg-gray-800 text-white items-center justify-center p-8" style="font-family: '${FONT_NAME}';">
          ${params.image ? `<img src="${params.image}" tw="absolute inset-0 w-full h-full object-cover opacity-20" />` : ''}
          <div tw="absolute inset-0 bg-gradient-to-br from-gray-900/85 via-purple-900/75 to-black/90"></div>

          <div tw="relative z-10 flex flex-col items-center text-center max-w-5xl p-8 bg-black/40 rounded-xl shadow-2xl">
            <h1 tw="text-5xl font-bold mb-3 text-purple-300" style="font-weight: 700;">
              ${params.title}
            </h1>
            ${params.subtitle ? `<h2 tw="text-3xl text-sky-300 mb-4" style="font-weight: 600;">${params.subtitle}</h2>` : ''}
            ${params.description ? `<p tw="text-xl text-slate-300 leading-relaxed max-w-3xl">${params.description}</p>` : ''}
            
            <div tw="mt-6 text-sm text-slate-400">anithing.moe / ${params.type.toUpperCase()}</div>
          </div>
        </div>
      `;
    }

    // --- Image Generation ---
    const cacheKey = generateCacheKey(params);
    const svg = await satori(satoriNode, satoriOptions);

    const resvg = new Resvg(svg, {
      fitTo: {
        mode: 'width',
        value: satoriOptions.width,
      },
    });
    const pngData = resvg.render();
    const pngBuffer = pngData.asPng();

    // --- Response ---
    const responseHeaders = {
      'Cache-Control': CACHE_CONTROL,
      'ETag': `"${cacheKey}"`,
      'Content-Type': 'image/png',
    };
    
    // SvelteKit's setHeaders can be used here. It will override headers in the Response object if keys match.
    setHeaders(responseHeaders);

    return new Response(pngBuffer, {
      status: 200,
      headers: responseHeaders,
    });

  } catch (e) {
    console.error('Error generating OG image:', e);
    // Check if it's a SvelteKit error object (has status and message)
    if (e && typeof e.status === 'number' && typeof e.message === 'string') {
      throw e; // Re-throw SvelteKit's error
    }
    // Otherwise, create a generic SvelteKit error
    throw error(500, `Failed to generate OG image: ${e.message || 'Unknown error'}`);
  }
};